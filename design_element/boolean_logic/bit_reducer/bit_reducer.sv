`timescale 1ns/1ps
`include "../include/package.svh"
module bit_reducer #( 
    parameter BIT_WIDTH = 8
)(
    input operation_e op_in,
    input [BIT_WIDTH-1:0] bits_in ,
    output logic  bit_out 
);

    always_comb begin : bit_reducer_logic
        case (op_in)
            AND: bit_out = &bits_in;
            NAND: bit_out = ~(&bits_in);
            OR: bit_out = |bits_in;
            NOR: bit_out = ~(|bits_in);
            XOR: bit_out = ^bits_in;
            XNOR: bit_out = ~(^bits_in);
            default: bit_out = 1'b0;
        endcase
    end
endmodule
