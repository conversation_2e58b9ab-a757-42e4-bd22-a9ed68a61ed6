`timescale 1ns/1ps
`include "../include/package.svh"
module word_reducer #(
    parameter WORD_WIDTH = 8,
    parameter WORD_CNT = 1
) (
    input operation_e op_in,
    input [WORD_WIDTH*WORD_CNT-1:0] words_in,
    output logic [WORD_WIDTH-1:0] words_out
);
    localparam  BIT_ZERO = {WORD_CNT{1'b0}};
    generate
        genvar i,j;
        for (j=0;j<WORD_WIDTH;j=j+1) begin : word_reducer_loop
            logic [WORD_CNT-1:0] words_in_j=BIT_ZERO;
            for (i=0;i<WORD_CNT;i=i+1) begin : word_reducer_loop_inner
                always@(*)
                    words_in_j[i] = words_in[i*WORD_WIDTH+j];
            end // word_reducer_loop_inner
            bit_reducer #(
                .BIT_WIDTH(WORD_CNT)
            ) bit_reducer_inst (
                .op_in(op_in),
                .bits_in(words_in_j),
                .bit_out(words_out[j])
            );
        end // word_reducer_loop
    endgenerate
    
endmodule