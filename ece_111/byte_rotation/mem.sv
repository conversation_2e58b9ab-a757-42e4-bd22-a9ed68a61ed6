`timescale 1ns/1ps
module mem #(
    parameter ADDR_WIDTH = 16,
    parameter DATA_WIDTH = 32
) (
    input logic clk,
    input logic [ADDR_WIDTH-1:0] mem_addr,
    input logic [DATA_WIDTH-1:0] mem_wdata,
    input logic wr_rd,  // 1 for write, 0 for read
    output logic [DATA_WIDTH-1:0] mem_rdata
);

  logic [DATA_WIDTH-1:0] mem_data[(1 << ADDR_WIDTH)-1:0];
  initial begin
      for (int i = 0; i < (1 << ADDR_WIDTH); i++) begin
          mem_data[i] = 0;
      end
  end

  always_ff @(posedge clk) begin
    if (wr_rd) begin
      // Write operation
      mem_data[mem_addr] <= mem_wdata;
    end else begin
      // Read operation
      mem_rdata <= mem_data[mem_addr];
    end
  end
endmodule
