import cocotb
from cocotb.triggers import RisingEdge,NextTimeStep
from cocotb.clock import Clock
import os
import random
from pathlib import Path

from cocotb.runner import get_runner


# Byte rotation reference model
def byte_rotate_ref(value):
    """Reference implementation of byte rotation function"""
    return ((value & 0x00FFFFFF) << 8) | ((value & 0xFF000000) >> 24)


async def reset_dut(dut):
    """Reset the DUT"""
    dut.rst_n.value = 0
    dut.start.value = 0
    dut.message_addr.value = 0
    dut.size.value = 0
    dut.output_addr.value = 0
    await RisingEdge(dut.clk)
    await RisingEdge(dut.clk)
    dut.rst_n.value = 1
    await RisingEdge(dut.clk)


async def start_operation(dut, message_addr, size, output_addr):
    """Start a byte rotation operation"""
    dut.message_addr.value = message_addr
    dut.size.value = size
    dut.output_addr.value = output_addr
    dut.start.value = 1
    await RisingEdge(dut.clk)
    dut.start.value = 0


async def wait_for_done(dut, max_cycles=1000, start_during_done=True):
    """Wait for the done signal to be asserted"""
    cycles = 0
    if start_during_done:
        await NextTimeStep()
    while not dut.done.value and cycles < max_cycles:
        await RisingEdge(dut.clk)
        if start_during_done:
            await NextTimeStep()
        cycles += 1

    if cycles >= max_cycles:
        raise TimeoutError(f"Operation did not complete within {max_cycles} cycles")

    return cycles


def get_memory_value(mem_module, addr):
    """Helper function to read memory value"""
    # Access the memory array in the mem module
    return int(mem_module.mem_data[addr].value)


def set_memory_value(mem_module, addr, value):
    """Helper function to set memory value"""
    # Set the memory array in the mem module
    mem_module.mem_data[addr].value = value


async def setup_memory_data(dut, start_addr, data_list):
    """Setup initial memory data for testing"""
    for i, data in enumerate(data_list):
        set_memory_value(dut.mem_inst, start_addr + i, data)
    # Wait a cycle for memory to settle
    await RisingEdge(dut.clk)


async def verify_memory_data(dut, start_addr, expected_data_list):
    """Verify memory data matches expected values"""
    for i, expected in enumerate(expected_data_list):
        actual = get_memory_value(dut.mem_inst, start_addr + i)
        assert actual == expected, f"Memory mismatch at addr {start_addr + i}: expected 0x{expected:08x}, got 0x{actual:08x}"


@cocotb.test()
async def test_byte_rotation_basic(dut):
    """Test basic functionality of byte rotation module"""

    dut._log.info("Starting basic byte rotation test")

    # Start clock
    clock = Clock(dut.clk, 10, units="ns")
    cocotb.start_soon(clock.start())

    # Reset
    await reset_dut(dut)

    # Test data - various patterns to test byte rotation
    test_data = [
        0x12345678,  # Normal pattern
        0xABCDEF00,  # Pattern with zeros
        0xFF000000,  # Only MSB set
        0x000000FF,  # Only LSB set
        0xFFFFFFFF,  # All ones
        0x00000000,  # All zeros
    ]

    message_addr = 0x100
    output_addr = 0x200
    size = len(test_data)

    # Setup memory with test data
    await setup_memory_data(dut, message_addr, test_data)

    dut._log.info(f"Testing with {size} words starting at addr 0x{message_addr:04x}")

    # Start operation
    await start_operation(dut, message_addr, size, output_addr)

    # Wait for completion
    cycles = await wait_for_done(dut)
    dut._log.info(f"Operation completed in {cycles} cycles")

    # Verify results
    expected_results = [byte_rotate_ref(data) for data in test_data]
    await verify_memory_data(dut, output_addr, expected_results)

    for i, (original, expected, actual) in enumerate(zip(test_data, expected_results,
                                                        [get_memory_value(dut.mem_inst, output_addr + j) for j in range(size)])):
        dut._log.info(f"Word {i}: 0x{original:08x} -> 0x{actual:08x} (expected 0x{expected:08x})")

    dut._log.info("Basic byte rotation test passed")


@cocotb.test()
async def test_byte_rotation_edge_cases(dut):
    """Test edge cases for byte rotation"""

    dut._log.info("Starting edge case tests")

    # Start clock
    clock = Clock(dut.clk, 10, units="ns")
    cocotb.start_soon(clock.start())

    # Reset
    await reset_dut(dut)

    # Test case 1: Single word
    dut._log.info("Testing single word operation")
    test_data = [0x12345678]
    message_addr = 0x50
    output_addr = 0x60

    await setup_memory_data(dut, message_addr, test_data)
    await start_operation(dut, message_addr, 1, output_addr)
    await wait_for_done(dut)

    expected = [byte_rotate_ref(0x12345678)]
    await verify_memory_data(dut, output_addr, expected)

    # Test case 2: Large size
    dut._log.info("Testing larger data set")
    large_test_data = [0x11111111 + i for i in range(16)]
    message_addr = 0x300
    output_addr = 0x400

    await setup_memory_data(dut, message_addr, large_test_data)
    await start_operation(dut, message_addr, len(large_test_data), output_addr)
    await wait_for_done(dut)

    expected_large = [byte_rotate_ref(data) for data in large_test_data]
    await verify_memory_data(dut, output_addr, expected_large)

    dut._log.info("Edge case tests passed")


@cocotb.test()
async def test_back_to_back_stimulus(dut):
    """Test critical back-to-back stimulus scenario"""

    dut._log.info("Starting back-to-back stimulus test")

    # Start clock
    clock = Clock(dut.clk, 10, units="ns")
    cocotb.start_soon(clock.start())

    # Reset
    await reset_dut(dut)

    # First operation data
    first_data = [0x12345678, 0xABCDEF00]
    first_message_addr = 0x100
    first_output_addr = 0x200
    first_size = len(first_data)

    # Second operation data
    second_data = [0xDEADBEEF, 0xCAFEBABE, 0x12345678]
    second_message_addr = 0x150
    second_output_addr = 0x250
    second_size = len(second_data)

    # Setup memory for both operations
    await setup_memory_data(dut, first_message_addr, first_data)
    await setup_memory_data(dut, second_message_addr, second_data)

    dut._log.info("Starting first operation")

    # Start first operation
    await start_operation(dut, first_message_addr, first_size, first_output_addr)

    # Wait for first operation to complete
    cycles1 = await wait_for_done(dut)
    dut._log.info(f"First operation completed in {cycles1} cycles")

    # Critical test: Apply second operation on the SAME cycle when done is asserted
    dut._log.info("Applying back-to-back stimulus on same cycle as done signal")

    # Verify we're in DONE state
    assert dut.done.value == 1, "Should be in DONE state"

    # Apply new inputs on the same cycle when done is high
    dut.message_addr.value = second_message_addr
    dut.size.value = second_size
    dut.output_addr.value = second_output_addr
    dut.start.value = 1

    # Wait one cycle and check FSM transition
    await RisingEdge(dut.clk)
    dut.start.value = 0


    # Wait for second operation to complete
    cycles2 = await wait_for_done(dut)
    dut._log.info(f"Second operation completed in {cycles2} cycles")

    # Verify both operations produced correct results
    expected_first = [byte_rotate_ref(data) for data in first_data]
    expected_second = [byte_rotate_ref(data) for data in second_data]

    await verify_memory_data(dut, first_output_addr, expected_first)
    await verify_memory_data(dut, second_output_addr, expected_second)

    dut._log.info("Back-to-back stimulus test passed - FSM correctly transitioned from DONE to WRITE")


@cocotb.test()
async def test_multiple_back_to_back_operations(dut):
    """Test multiple consecutive back-to-back operations"""

    dut._log.info("Starting multiple back-to-back operations test")

    # Start clock
    clock = Clock(dut.clk, 10, units="ns")
    cocotb.start_soon(clock.start())

    # Reset
    await reset_dut(dut)

    # Define multiple operations
    operations = [
        {"data": [0x11111111], "msg_addr": 0x100, "out_addr": 0x200},
        {"data": [0x22222222, 0x33333333], "msg_addr": 0x110, "out_addr": 0x210},
        {"data": [0x44444444, 0x55555555, 0x66666666], "msg_addr": 0x120, "out_addr": 0x220},
        {"data": [0x77777777], "msg_addr": 0x130, "out_addr": 0x230},
    ]

    # Setup memory for all operations
    for op in operations:
        await setup_memory_data(dut, op["msg_addr"], op["data"])

    # Execute operations back-to-back
    for i, op in enumerate(operations):
        dut._log.info(f"Starting operation {i+1}/{len(operations)}")

        if i == 0:
            # First operation - normal start
            await start_operation(dut, op["msg_addr"], len(op["data"]), op["out_addr"])
        else:
            # Subsequent operations - back-to-back stimulus
            # Wait for previous operation to complete
            await wait_for_done(dut)

            # Apply new operation on same cycle as done
            dut.message_addr.value = op["msg_addr"]
            dut.size.value = len(op["data"])
            dut.output_addr.value = op["out_addr"]
            dut.start.value = 1
            await RisingEdge(dut.clk)
            dut.start.value = 0

    # Wait for final operation to complete
    await wait_for_done(dut)

    # Verify all operations produced correct results
    for i, op in enumerate(operations):
        expected = [byte_rotate_ref(data) for data in op["data"]]
        await verify_memory_data(dut, op["out_addr"], expected)
        dut._log.info(f"Operation {i+1} results verified")

    dut._log.info("Multiple back-to-back operations test passed")


@cocotb.test()
async def test_fsm_state_transitions(dut):
    """Test FSM state transitions in detail"""

    dut._log.info("Starting FSM state transition test")

    # Start clock
    clock = Clock(dut.clk, 10, units="ns")
    cocotb.start_soon(clock.start())

    # Reset
    await reset_dut(dut)

    # Setup test data
    test_data = [0x12345678, 0xABCDEF00]
    message_addr = 0x100
    output_addr = 0x200
    size = len(test_data)

    await setup_memory_data(dut, message_addr, test_data)

    # Monitor FSM states during operation
    # Note: We can't directly access internal state signals, but we can infer states
    # from the external behavior

    # Initial state should be IDLE (done = 0, no memory operations)
    assert dut.done.value == 0, "Should start in IDLE state"

    # Start operation
    await start_operation(dut, message_addr, size, output_addr)

    # Monitor the operation cycle by cycle
    operation_cycles = 0
    write_cycles = 0
    read_cycles = 0

    while not dut.done.value and operation_cycles < 100:
        await RisingEdge(dut.clk)
        operation_cycles += 1

        # Count write and read operations
        if dut.wr_rd.value == 1:
            write_cycles += 1
        elif dut.wr_rd.value == 0:
            read_cycles += 1

    # Should reach DONE state
    assert dut.done.value == 1, "Should reach DONE state"

    dut._log.info(f"FSM completed operation in {operation_cycles} cycles")
    dut._log.info(f"Write cycles: {write_cycles}, Read cycles: {read_cycles}")

    # For size=2, we expect alternating WRITE/READ cycles
    # The exact count depends on the FSM implementation
    assert write_cycles > 0, "Should have performed write operations"

    dut._log.info("FSM state transition test passed")


@cocotb.test()
async def test_random_data_patterns(dut):
    """Test with random data patterns"""

    dut._log.info("Starting random data pattern test")

    # Start clock
    clock = Clock(dut.clk, 10, units="ns")
    cocotb.start_soon(clock.start())

    # Reset
    await reset_dut(dut)

    # Generate random test data
    random.seed(42)  # For reproducible tests
    random_data = [random.randint(0, 0xFFFFFFFF) for _ in range(8)]

    message_addr = 0x500
    output_addr = 0x600
    size = len(random_data)

    await setup_memory_data(dut, message_addr, random_data)

    dut._log.info(f"Testing with {size} random data words")

    # Start operation
    await start_operation(dut, message_addr, size, output_addr)

    # Wait for completion
    cycles = await wait_for_done(dut)
    dut._log.info(f"Random data operation completed in {cycles} cycles")

    # Verify results
    expected_results = [byte_rotate_ref(data) for data in random_data]
    await verify_memory_data(dut, output_addr, expected_results)

    # Log some examples
    for i in range(min(4, len(random_data))):
        original = random_data[i]
        expected = expected_results[i]
        actual = get_memory_value(dut.mem_inst, output_addr + i)
        dut._log.info(f"Random {i}: 0x{original:08x} -> 0x{actual:08x} (expected 0x{expected:08x})")

    dut._log.info("Random data pattern test passed")


@cocotb.test()
async def test_boundary_addresses(dut):
    """Test with boundary memory addresses"""

    dut._log.info("Starting boundary address test")

    # Start clock
    clock = Clock(dut.clk, 10, units="ns")
    cocotb.start_soon(clock.start())

    # Reset
    await reset_dut(dut)

    # Test with address 0
    test_data = [0x12345678]
    await setup_memory_data(dut, 0, test_data)

    await start_operation(dut, 0, 1, 1)
    await wait_for_done(dut)

    expected = [byte_rotate_ref(0x12345678)]
    await verify_memory_data(dut, 1, expected)

    dut._log.info("Boundary address test passed")


def test_run():
    """Run the test suite"""
    tests_dir = Path(__file__).parent
    print(f"Running tests from: {tests_dir}")
    hdl_dir = tests_dir
    sim = os.getenv("SIM", "icarus")
    runner = get_runner(sim)

    # Build the simulation
    runner.build(
        verilog_sources=[
            hdl_dir / "byte_rotation_tb.sv",
            hdl_dir / "byte_rotation.sv",
            hdl_dir / "mem.sv"
        ],
        hdl_toplevel="byte_rotation_tb",
        parameters={
            "DATA_WIDTH": "32",
            "ADDR_WIDTH": "16"
        },
        waves=True,
        build_dir=f"sim_build/byte_rotation",
    )

    # Run the tests
    runner.test(
        test_module="test_byte_rotate",
        hdl_toplevel="byte_rotation_tb",
        parameters={
            "DATA_WIDTH": "32",
            "ADDR_WIDTH": "16"
        },
        extra_env={}
    )


if __name__ == "__main__":
    test_run()