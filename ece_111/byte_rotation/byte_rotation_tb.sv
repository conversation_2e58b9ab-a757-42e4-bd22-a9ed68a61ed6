`timescale 1ns/1ps
module byte_rotation_tb #(
    parameter DATA_WIDTH = 32,
    parameter ADDR_WIDTH = 16
) (
    input logic             clk,
    input logic             rst_n,
    input logic    [ADDR_WIDTH-1:0] message_addr,
    input logic    [ADDR_WIDTH-1:0] size,
    input logic [ADDR_WIDTH-1:0] output_addr,
    input logic start,
    output logic done
);

    // Internal signals for memory interface
    logic         mem_clk;
    logic         [ADDR_WIDTH-1:0] mem_addr;
    logic         [DATA_WIDTH-1:0] mem_wdata;
    logic wr_rd; // 1 for write, 0 for read
    logic [DATA_WIDTH-1:0] mem_rdata;

    // Instantiate the byte rotation module
    byte_rotation #(
        .DATA_WIDTH(DATA_WIDTH),
        .ADDR_WIDTH(ADDR_WIDTH)
    ) dut (
        .clk(clk),
        .rst_n(rst_n),
        .message_addr(message_addr),
        .size(size),
        .output_addr(output_addr),
        .start(start),
        .done(done),
        .mem_clk(mem_clk),
        .mem_addr(mem_addr),
        .mem_wdata(mem_wdata),
        .wr_rd(wr_rd),
        .mem_rdata(mem_rdata)
    );

    // Instantiate the memory module
    mem #(
        .ADDR_WIDTH(ADDR_WIDTH),
        .DATA_WIDTH(DATA_WIDTH)
    ) mem_inst (
        .clk(mem_clk),
        .mem_addr(mem_addr),
        .mem_wdata(mem_wdata),
        .wr_rd(wr_rd),
        .mem_rdata(mem_rdata)
    );

endmodule
