`timescale 1ns/1ps
module byte_rotation #(
    parameter DATA_WIDTH = 32,
    parameter ADDR_WIDTH = 16
) (
    input logic             clk,
    input logic             rst_n,
    input logic    [ADDR_WIDTH-1:0] message_addr,
    input logic    [ADDR_WIDTH-1:0] size,
    input logic [ADDR_WIDTH-1:0] output_addr,
    input logic start,
    output logic done,

    // mem interface
    output logic         mem_clk,
    output logic         [ADDR_WIDTH-1:0] mem_addr,
    output logic         [DATA_WIDTH-1:0] mem_wdata,
    output logic wr_rd, // 1 for write, 0 for read
    input logic [DATA_WIDTH-1:0] mem_rdata
);
    function logic [31:0] byte_rotate (input logic [31:0] value);
        byte_rotate = {value[23:0], value[31:24]};
    endfunction

    typedef  enum logic [1:0] {
        IDLE = 0,
        WRITE,
        READ,DONE
    }state_t;

    state_t cur_state, next_state;
    logic [ADDR_WIDTH-1:0] mem_raddr,mem_waddr;
    logic [ADDR_WIDTH-1:0] count;

    
    assign mem_clk = clk;

    always @(posedge clk or negedge rst_n) begin
        if(~rst_n) begin
            mem_raddr <='b0;
            mem_waddr <='0;
        end else if(start) begin
            mem_raddr <= message_addr;
            mem_waddr <= output_addr;
        end
    end

    always_comb begin : mem_logic
        mem_wdata = '0;
        mem_addr = '0;
        wr_rd = 0; // Default to no operation
        case (cur_state)
            IDLE: begin
                mem_addr = message_addr;
                wr_rd = 0; // Read operation
            end
            WRITE: begin
                mem_addr = mem_waddr + count;
                wr_rd = 1; // Write operation
                mem_wdata = byte_rotate(mem_rdata);
            end
            READ: begin
                mem_addr = mem_raddr + count;
                wr_rd = 0; // Read operation
            end
            DONE: begin
                mem_addr = message_addr;
                wr_rd = 0; // No operation
            end
        endcase
    end

    always_comb begin : next_state_logic
        case (cur_state)
            IDLE: begin
                if (start) begin
                    next_state = WRITE;
                end else begin
                    next_state = IDLE;
                end
            end
            WRITE: begin
                if (count < size -1) begin
                    next_state = READ;
                end else begin
                    next_state = DONE;
                end
            end
            READ: begin
                if (count < size ) begin
                    next_state = WRITE;
                end else begin
                    next_state = DONE;
                end
            end
            DONE: begin
                next_state = IDLE; // Go back to IDLE after completion
                if (start)
                next_state = WRITE;
            end
            default: next_state = IDLE; // Default state is IDLE
        endcase
    end

    always_ff @( posedge clk or negedge rst_n ) begin 
        if (~rst_n) begin
            cur_state <= IDLE;
            count <= '0;
        end else begin
            cur_state <= next_state;
            case (cur_state)
                IDLE: begin
                    count <= '0;
                end
                WRITE: begin
                    count <= count + 1;
                end
                DONE: begin
                    count <= '0;
                end
                default: begin
                end
            endcase
        end
    end
    assign done = (cur_state == DONE);
endmodule
